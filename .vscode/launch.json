{"version": "0.2.0", "configurations": [{"name": "NLP.Api", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/NLP/RealPlusNLP.Api/bin/Debug/net9.0/RealPlusNLP.Api.dll", "args": [], "cwd": "${workspaceFolder}/src/NLP/RealPlusNLP.Api", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}]}