{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Extensions.AI": "Information", "Azure.AI.OpenAI": "Information"}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": ["https://localhost:44393", "https://*.realplusonline.com"]}, "AppAuthentication": {"ApiKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlMzYwZjIwZS1mZjYwLTQwZjYtYjIwZi1mZjYwZjYwZjYwZjAiLCJpYXQiOjE1NzYwNjYwNzcsImp0aSI6IjIwMjAtMDItMjJUMDk6MzA6MjcuNjYwNjA2WiIsImV4cCI6MTU3NjA2NjA3OH0.1"}, "AzureOpenAI": {"ModelId": "gpt-4o"}, "OpenTelemetryOptions": {"ServiceName": "nlp.api.prod", "NewrelicEndpoint": "https://otlp.nr-data.net"}, "ElasticsearchOptions": {"Url": "https://rpls-prod-elasticsearch.es.us-central1.gcp.cloud.es.io", "Username": "elastic", "Password": "a0RVdd5X67pyZmXcQ5FK3J6g", "BuildingIndexName": "building-search", "SchoolIndexName": "school", "CompanyIndexName": "company-search"}}