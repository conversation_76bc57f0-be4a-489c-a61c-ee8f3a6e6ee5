using System.ComponentModel;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class BuildingPlugin(IElasticsearchService elasticsearchService)
{
    [Description("Returns the building ids for given building names in the New York City")]
    public async Task<BuildingModel[]> GetBuildings(
        [Description("A list of building names in the New York City, such as 'Trump Palace', 'One World Trade Center', etc.")]
        List<string> buildingNames,
        CancellationToken cancellationToken = default)
    {
        var searchTasks = buildingNames
            .Select(buildingName => elasticsearchService.SearchBuildingsAsync(buildingName, cancellationToken));

        var searchResults = await Task.WhenAll(searchTasks);

        var buildings = searchResults
            .SelectMany(result => result)
            .ToArray();

        return buildings;
    }
}
