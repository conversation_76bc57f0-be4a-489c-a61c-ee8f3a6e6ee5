using System.Linq.Expressions;
using Microsoft.Extensions.Options;
using Nest;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Documents;
using RealPlusNLP.Api.Common.Models;
using RealPlusNLP.Api.Configuration.Options;

namespace RealPlusNLP.Api.Infrastructure.Services;

public class ElasticsearchService(
    IElasticClient client,
    IOptions<ElasticsearchOptions> options)
    : IElasticsearchService
{
    private const int MaxResults = 5;

    private readonly ElasticsearchOptions _options = options.Value;

    public async Task<IReadOnlyCollection<BuildingModel>> SearchBuildingsAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        static QueryContainer Prefix(string searchParam, QueryContainerDescriptor<PropertyDocument> q)
        {
            return q.Prefix(p => p.Field(f => f.BuildingName).Value(searchParam));
        }

        static QueryContainer MatchPhrasePrefix(string searchParam, QueryContainerDescriptor<PropertyDocument> q)
        {
            return q.MatchPhrasePrefix(p => p.Field(f => f.BuildingName).Query(searchParam));
        }

        var response = await client.SearchAsync<PropertyDocument>(s => s
            .Index($"resource-{_options.BuildingIndexName}")
            .Size(1000)
            .Source(ss => ss.Includes(o => o.Fields(f => f.BuildingName, f => f.RPBin)))
            .Query(q => q.Bool(
                b => b.Should(
                    should => MatchPhrasePrefix(searchText, should),
                    should => Prefix(searchText, should)
            )))
            , cancellationToken);

        return response.Documents.GroupBy(document => document.BuildingName.ToUpper())
            .Select(groupItem => new BuildingModel
            (
                groupItem.Key,
                [.. groupItem.Select(item => item.RPBin ?? 0).OrderBy(item => item)]
            )).Take(MaxResults).ToList().AsReadOnly();
    }

    public async Task<IReadOnlyCollection<SchoolDocument>> SearchSchoolsAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        var searchString = searchText.ToLower();

        var response = await client.SearchAsync<SchoolDocument>(sd => sd
            .Size(MaxResults)
            .Query(q => q
                .Bool(b => b
                    .Must(
                        m => m.Term(e => e.Field(f => f.HasZone).Value(true)),
                        s => s.Bool(bb => bb.Should(
                            ss => ss.Match(o => o.Field(f => f.Name).Query(searchString)),
                            ss => ss.Match(o => o.Field(f => f.Name.Suffix("classic"))
                                .Query(searchString).Operator(Operator.And)))))))
            , cancellationToken);

        return response.Documents.ToList().AsReadOnly();
    }

    public async Task<IReadOnlyCollection<CompanyDocument>> SearchCompaniesAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        var searchString = searchText.ToLower();

        var response = await client.SearchAsync<CompanyDocument>(sd => sd
            .Index($"resource-{_options.CompanyIndexName}")
            .Size(MaxResults)
            .Query(q => q
                .Bool(b => b
                    .Should(
                        ss => ss.Prefix(p => p.Field(f => f.CompanyCode).Value(searchString)),
                        ss => ss.Prefix(p => p.Field(f => f.CompanyName).Value(searchString)),
                        ss => ss.MatchPhrasePrefix(p => p.Field(f => f.CompanyName)
                            .Query(searchString)))))
            , cancellationToken);

        return response.Documents.ToList().AsReadOnly();
    }

    public async Task<BuildingManagementModel> SearchBuildingManagementAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        SearchDescriptor<PropertyDocument> CreateCompanyQuery(
            SearchDescriptor<PropertyDocument> searchDescriptor,
            Expression<Func<PropertyDocument, object>> field,
            string value)
        {
            return searchDescriptor
                .Index($"resource-{_options.BuildingIndexName}")
                .Size(MaxResults * 100)
                .Source(src => src.Includes(incl => incl.Field(field)))
                .Query(q => q
                    .Bool(b => b.Should(
                        bs => bs.MatchBoolPrefix(p => p.Field(field).Query(value)),
                        bs => bs.Prefix(p => p.Field(field).Value(value)))));
        }

        SearchDescriptor<PropertyDocument> CreateQuery(
            SearchDescriptor<PropertyDocument> searchDescriptor,
            Expression<Func<PropertyDocument, object>> field,
            Expression<Func<PropertyDocument, object>> aggregationField,
            string value)
        {
            return searchDescriptor
                    .Index($"resource-{_options.BuildingIndexName}")
                    .Size(MaxResults)
                    .Source(src => src.Includes(incl => incl.Field(field)))
                    .Collapse(c => c.Field(aggregationField))
                    .Query(q => q
                        .Bool(b => b.Should(
                            bs => bs.MatchBoolPrefix(p => p.Field(field).Query(value)),
                            bs => bs.Prefix(p => p.Field(field).Value(value)))));
        }

        var searchString = searchText.ToLower();

        var response = await client.MultiSearchAsync(selector: ms =>
        {
            ms.Search<PropertyDocument>("Company", s =>
                CreateCompanyQuery(s, f => f.ManagingAgent, searchText));
            ms.Search<PropertyDocument>("Landlord", s =>
                CreateQuery(s, f => f.Landlord, f => f.Landlord.Suffix("keyword"), searchText));
            ms.Search<PropertyDocument>("Owner", s =>
                CreateQuery(s, f => f.Owner, f => f.Owner.Suffix("keyword"), searchText));

            return ms;
        }, ct: cancellationToken);

        var resultCompany = response.GetResponse<PropertyDocument>("Company");
        var resultLandlord = response.GetResponse<PropertyDocument>("Landlord");
        var resultOwner = response.GetResponse<PropertyDocument>("Owner");

        return new BuildingManagementModel(
            [.. resultCompany.Documents.SelectMany(d => d.ManagingAgent).Distinct().Take(MaxResults)],
            [.. resultOwner.Documents.Select(d => d.Owner).Distinct()],
            [.. resultLandlord.Documents.Select(d => d.Landlord).Distinct()]);
    }
}
