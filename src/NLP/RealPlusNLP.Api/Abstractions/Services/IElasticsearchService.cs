using RealPlusNLP.Api.Common.Documents;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Abstractions.Services;

public interface IElasticsearchService
{
    Task<IReadOnlyCollection<BuildingModel>> SearchBuildingsAsync(
        string searchText, CancellationToken cancellationToken = default);

    Task<IReadOnlyCollection<SchoolDocument>> SearchSchoolsAsync(
        string searchText, CancellationToken cancellationToken = default);

    Task<IReadOnlyCollection<CompanyDocument>> SearchCompaniesAsync(
        string searchText, CancellationToken cancellationToken = default);

    Task<BuildingManagementModel> SearchBuildingManagementAsync(
        string searchText, CancellationToken cancellationToken = default);
}
